INFO:main:Directory ensured: storyteller_cards/pdfs
INFO:main:Directory ensured: storyteller_cards/card_sets
INFO:main:Directory ensured: storyteller_cards/story_patterns
INFO:main:Directory ensured: storyteller_cards/progress
INFO:main:Directory ensured: storyteller_cards/database
INFO:database:Database initialized successfully
INFO:main:Database initialized successfully
INFO:     Started server process [70197]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
INFO:     127.0.0.1:58564 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20N<PERSON><PERSON>er%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58606 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20<PERSON><PERSON><PERSON><PERSON>%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58700 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:58700 - "GET /static/styles.css HTTP/1.1" 304 Not Modified
INFO:     127.0.0.1:58702 - "GET /static/app.js HTTP/1.1" 200 OK
INFO:     127.0.0.1:58702 - "GET /api/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:58702 - "GET /api/config HTTP/1.1" 200 OK
INFO:     127.0.0.1:58702 - "GET /api/pdfs HTTP/1.1" 200 OK
INFO:     127.0.0.1:58702 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58702 - "GET /api/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:58708 - "GET /api/pdfs HTTP/1.1" 200 OK
INFO:     127.0.0.1:58708 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58708 - "GET /api/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/pdfs HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/pdfs HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/pdfs HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/pdfs HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/pdfs HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/pdfs HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/pdfs HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/pdfs HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/pdfs HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/pdfs HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/pdfs HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58710 - "GET /api/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:58734 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:58736 - "GET /static/styles.css HTTP/1.1" 200 OK
INFO:     127.0.0.1:58737 - "GET /static/app.js HTTP/1.1" 200 OK
INFO:     127.0.0.1:58737 - "GET /api/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:58737 - "GET /api/config HTTP/1.1" 200 OK
INFO:main:Uploaded file: The 46 Rules of Genius An Innovators Guide To Creativity (Marty Neumeier).pdf (3597745 bytes)
INFO:     127.0.0.1:58738 - "POST /api/upload HTTP/1.1" 200 OK
INFO:     127.0.0.1:58747 - "POST /api/process/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf HTTP/1.1" 200 OK
INFO:google_genai.models:AFC is enabled with max remote calls: 10.
INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
INFO:google_genai.models:AFC remote call 1 is done.
ERROR:main:Error analyzing story potential: Unterminated string starting at: line 59 column 16 (char 1217)
INFO:database:Cleared existing cards for PDF The 46 Rules of Genius An Innovators Guide To Creativity (Marty Neumeier).pdf
INFO:     127.0.0.1:58747 - "GET /api/status/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf_1748296346 HTTP/1.1" 200 OK
INFO:     127.0.0.1:58747 - "GET /api/status/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf_1748296346 HTTP/1.1" 200 OK
INFO:google_genai.models:AFC is enabled with max remote calls: 10.
INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
INFO:google_genai.models:AFC remote call 1 is done.
ERROR:main:Error analyzing story potential: Unterminated string starting at: line 44 column 16 (char 907)
INFO:     127.0.0.1:58747 - "GET /api/status/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf_1748296346 HTTP/1.1" 200 OK
INFO:     127.0.0.1:58747 - "GET /api/status/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf_1748296346 HTTP/1.1" 200 OK
INFO:google_genai.models:AFC is enabled with max remote calls: 10.
INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
INFO:google_genai.models:AFC remote call 1 is done.
ERROR:main:Error analyzing story potential: Unterminated string starting at: line 35 column 16 (char 736)
INFO:     127.0.0.1:58758 - "GET /api/status/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf_1748296346 HTTP/1.1" 200 OK
INFO:     127.0.0.1:58758 - "GET /api/status/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf_1748296346 HTTP/1.1" 200 OK
INFO:google_genai.models:AFC is enabled with max remote calls: 10.
INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
INFO:google_genai.models:AFC remote call 1 is done.
ERROR:main:Error analyzing story potential: Unterminated string starting at: line 47 column 16 (char 961)
INFO:database:Cleared existing cards for PDF The 46 Rules of Genius An Innovators Guide To Creativity (Marty Neumeier).pdf
INFO:     127.0.0.1:58758 - "GET /api/status/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf_1748296346 HTTP/1.1" 200 OK
INFO:     127.0.0.1:58759 - "GET /api/status/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf_1748296346 HTTP/1.1" 200 OK
INFO:google_genai.models:AFC is enabled with max remote calls: 10.
INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
INFO:google_genai.models:AFC remote call 1 is done.
ERROR:main:Error analyzing story potential: Unterminated string starting at: line 42 column 16 (char 924)
INFO:database:Cleared existing cards for PDF The 46 Rules of Genius An Innovators Guide To Creativity (Marty Neumeier).pdf
INFO:database:Cleared existing cards for PDF The 46 Rules of Genius An Innovators Guide To Creativity (Marty Neumeier).pdf
INFO:     127.0.0.1:58759 - "GET /api/status/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf_1748296346 HTTP/1.1" 200 OK
INFO:     127.0.0.1:58759 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58759 - "GET /api/stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:58762 - "GET /api/pdfs HTTP/1.1" 200 OK
INFO:     127.0.0.1:58762 - "GET /api/cards/The%2046%20Rules%20of%20Genius%20An%20Innovators%20Guide%20To%20Creativity%20%28Marty%20Neumeier%29.pdf HTTP/1.1" 200 OK
INFO:     127.0.0.1:58762 - "GET /api/stats HTTP/1.1" 200 OK
