"""
Database module for Storyteller Tactics Card Generator
Implements SQLite storage with JSON fallback for backward compatibility
"""
import sqlite3
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Any
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class StoryDatabase:
    def __init__(self, db_path: Path):
        self.db_path = db_path
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """Initialize database with tables and indexes"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Create tables
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS pdfs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        filename TEXT UNIQUE NOT NULL,
                        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        total_pages INTEGER,
                        processing_status TEXT DEFAULT 'pending',
                        existing_cards INTEGER DEFAULT 0
                    )
                """)
                
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS story_cards (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        pdf_id INTEGER,
                        title TEXT NOT NULL,
                        framework TEXT,
                        category TEXT,
                        emotional_core TEXT,
                        content TEXT,
                        key_insight TEXT,
                        contextual_relevance TEXT,
                        storyteller_script TEXT,
                        script_hook TEXT,
                        script_narrative TEXT,
                        script_conclusion TEXT,
                        script_delivery TEXT,
                        key_phrases TEXT,  -- JSON array
                        audience_impact TEXT,
                        interest_score INTEGER,
                        audience TEXT,  -- JSON array
                        source TEXT,
                        page_reference TEXT,  -- JSON array
                        entities TEXT,  -- JSON array
                        sentiment TEXT,
                        keywords TEXT,  -- JSON array
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (pdf_id) REFERENCES pdfs (id)
                    )
                """)
                
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS processing_progress (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        pdf_filename TEXT NOT NULL,
                        last_processed_chunk INTEGER DEFAULT 0,
                        total_chunks INTEGER,
                        cards_found INTEGER DEFAULT 0,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create indexes for better performance
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_pdf_filename ON pdfs(filename)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_card_pdf ON story_cards(pdf_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_card_category ON story_cards(category)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_card_framework ON story_cards(framework)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_card_score ON story_cards(interest_score)")
                
                # Create full-text search table
                cursor.execute("""
                    CREATE VIRTUAL TABLE IF NOT EXISTS story_cards_fts USING fts5(
                        title, content, key_insight, storyteller_script, keywords,
                        content=story_cards,
                        content_rowid=id
                    )
                """)
                
                # Create triggers to keep FTS in sync
                cursor.execute("""
                    CREATE TRIGGER IF NOT EXISTS story_cards_fts_insert AFTER INSERT ON story_cards
                    BEGIN
                        INSERT INTO story_cards_fts(rowid, title, content, key_insight, storyteller_script, keywords)
                        VALUES (new.id, new.title, new.content, new.key_insight, new.storyteller_script, new.keywords);
                    END
                """)
                
                cursor.execute("""
                    CREATE TRIGGER IF NOT EXISTS story_cards_fts_delete AFTER DELETE ON story_cards
                    BEGIN
                        DELETE FROM story_cards_fts WHERE rowid = old.id;
                    END
                """)
                
                cursor.execute("""
                    CREATE TRIGGER IF NOT EXISTS story_cards_fts_update AFTER UPDATE ON story_cards
                    BEGIN
                        UPDATE story_cards_fts 
                        SET title = new.title, content = new.content, key_insight = new.key_insight,
                            storyteller_script = new.storyteller_script, keywords = new.keywords
                        WHERE rowid = new.id;
                    END
                """)
                
                conn.commit()
                logger.info("Database initialized successfully")
                
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Get database connection with automatic cleanup"""
        conn = sqlite3.connect(str(self.db_path))
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def save_pdf_info(self, filename: str, total_pages: int, existing_cards: int = 0) -> Optional[int]:
        """Save PDF information to database"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO pdfs (filename, total_pages, existing_cards)
                    VALUES (?, ?, ?)
                """, (filename, total_pages, existing_cards))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"Error saving PDF info: {e}")
            return None
    
    def get_pdf_id(self, filename: str) -> Optional[int]:
        """Get PDF ID by filename"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT id FROM pdfs WHERE filename = ?", (filename,))
                result = cursor.fetchone()
                return result['id'] if result else None
        except Exception as e:
            logger.error(f"Error getting PDF ID: {e}")
            return None
    
    def save_story_cards(self, pdf_filename: str, cards: List[Dict[str, Any]]) -> bool:
        """Save story cards to database"""
        pdf_id = self.get_pdf_id(pdf_filename)
        if not pdf_id:
            pdf_id = self.save_pdf_info(pdf_filename, 0)
        
        if not pdf_id:
            return False
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # First, delete existing cards for this PDF to avoid duplicates
                cursor.execute("DELETE FROM story_cards WHERE pdf_id = ?", (pdf_id,))
                logger.info(f"Cleared existing cards for PDF {pdf_filename}")
                
                for card in cards:
                    # Convert lists and dicts to JSON strings
                    # Handle storyteller_script which might be a dict
                    storyteller_script = card.get('storyteller_script', '')
                    if isinstance(storyteller_script, dict):
                        storyteller_script = json.dumps(storyteller_script)
                    elif not isinstance(storyteller_script, str):
                        storyteller_script = str(storyteller_script)
                    
                    cursor.execute("""
                        INSERT INTO story_cards (
                            pdf_id, title, framework, category, emotional_core, content,
                            key_insight, contextual_relevance, storyteller_script,
                            script_hook, script_narrative, script_conclusion, script_delivery,
                            key_phrases, audience_impact, interest_score, audience,
                            source, page_reference, entities, sentiment, keywords
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        pdf_id,
                        str(card.get('title', '')),
                        str(card.get('framework', '')),
                        str(card.get('category', '')),
                        str(card.get('emotional_core', '')),
                        str(card.get('content', '')),
                        str(card.get('key_insight', '')),
                        str(card.get('contextual_relevance', '')),
                        storyteller_script,
                        str(card.get('script_hook', '')),
                        str(card.get('script_narrative', '')),
                        str(card.get('script_conclusion', '')),
                        str(card.get('script_delivery', '')),
                        json.dumps(card.get('key_phrases', [])),
                        str(card.get('audience_impact', '')),
                        int(card.get('interest_score', 0)),
                        json.dumps(card.get('audience', [])),
                        str(card.get('source', '')),
                        json.dumps(card.get('page_reference', [])),
                        json.dumps(card.get('entities', [])),
                        str(card.get('sentiment', '')),
                        json.dumps(card.get('keywords', []))
                    ))
                
                # Update PDF card count
                cursor.execute("""
                    UPDATE pdfs SET existing_cards = 
                    (SELECT COUNT(*) FROM story_cards WHERE pdf_id = ?)
                    WHERE id = ?
                """, (pdf_id, pdf_id))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"Error saving story cards: {e}")
            # Log the problematic card for debugging
            if 'card' in locals():
                logger.error(f"Problematic card: {json.dumps(card, indent=2, default=str)}")
            return False
    
    def get_story_cards(self, pdf_filename: str) -> List[Dict[str, Any]]:
        """Get all story cards for a PDF"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT sc.* FROM story_cards sc
                    JOIN pdfs p ON sc.pdf_id = p.id
                    WHERE p.filename = ?
                    ORDER BY sc.interest_score DESC
                """, (pdf_filename,))
                
                cards = []
                for row in cursor.fetchall():
                    card = dict(row)
                    # Parse JSON fields
                    card['key_phrases'] = json.loads(card['key_phrases'] or '[]')
                    card['audience'] = json.loads(card['audience'] or '[]')
                    card['page_reference'] = json.loads(card['page_reference'] or '[]')
                    card['entities'] = json.loads(card['entities'] or '[]')
                    card['keywords'] = json.loads(card['keywords'] or '[]')
                    cards.append(card)
                
                return cards
                
        except Exception as e:
            logger.error(f"Error getting story cards: {e}")
            return []
    
    def search_cards(self, query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Search story cards with optional filters"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Base query using FTS
                sql = """
                    SELECT sc.*, p.filename as pdf_filename
                    FROM story_cards sc
                    JOIN pdfs p ON sc.pdf_id = p.id
                    WHERE sc.id IN (
                        SELECT rowid FROM story_cards_fts WHERE story_cards_fts MATCH ?
                    )
                """
                params = [query]
                
                # Apply filters
                if filters:
                    if filters.get('category'):
                        sql += " AND sc.category = ?"
                        params.append(filters['category'])
                    
                    if filters.get('framework'):
                        sql += " AND sc.framework = ?"
                        params.append(filters['framework'])
                    
                    if filters.get('min_score'):
                        sql += " AND sc.interest_score >= ?"
                        params.append(filters['min_score'])
                    
                    if filters.get('sentiment'):
                        sql += " AND sc.sentiment = ?"
                        params.append(filters['sentiment'])
                    
                    if filters.get('pdf_filename'):
                        sql += " AND p.filename = ?"
                        params.append(filters['pdf_filename'])
                
                sql += " ORDER BY sc.interest_score DESC LIMIT 50"
                
                cursor.execute(sql, params)
                
                cards = []
                for row in cursor.fetchall():
                    card = dict(row)
                    # Parse JSON fields
                    card['key_phrases'] = json.loads(card['key_phrases'] or '[]')
                    card['audience'] = json.loads(card['audience'] or '[]')
                    card['page_reference'] = json.loads(card['page_reference'] or '[]')
                    card['entities'] = json.loads(card['entities'] or '[]')
                    card['keywords'] = json.loads(card['keywords'] or '[]')
                    cards.append(card)
                
                return cards
                
        except Exception as e:
            logger.error(f"Error searching cards: {e}")
            return []
    
    def save_progress(self, pdf_filename: str, last_chunk: int, total_chunks: int, cards_found: int):
        """Save processing progress"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO processing_progress 
                    (pdf_filename, last_processed_chunk, total_chunks, cards_found, updated_at)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (pdf_filename, last_chunk, total_chunks, cards_found))
                conn.commit()
        except Exception as e:
            logger.error(f"Error saving progress: {e}")
    
    def get_progress(self, pdf_filename: str) -> Optional[Dict[str, Any]]:
        """Get processing progress"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM processing_progress WHERE pdf_filename = ?
                    ORDER BY updated_at DESC LIMIT 1
                """, (pdf_filename,))
                result = cursor.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logger.error(f"Error getting progress: {e}")
            return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get overall statistics"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Total PDFs
                cursor.execute("SELECT COUNT(*) as count FROM pdfs")
                total_pdfs = cursor.fetchone()['count']
                
                # Total cards
                cursor.execute("SELECT COUNT(*) as count FROM story_cards")
                total_cards = cursor.fetchone()['count']
                
                # Category distribution
                cursor.execute("""
                    SELECT category, COUNT(*) as count 
                    FROM story_cards 
                    GROUP BY category
                """)
                categories = {row['category']: row['count'] for row in cursor.fetchall()}
                
                # Average score
                cursor.execute("SELECT AVG(interest_score) as avg_score FROM story_cards")
                avg_score = cursor.fetchone()['avg_score'] or 0
                
                return {
                    'total_pdfs': total_pdfs,
                    'total_cards': total_cards,
                    'categories': categories,
                    'average_score': round(avg_score, 2)
                }
                
        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            return {
                'total_pdfs': 0,
                'total_cards': 0,
                'categories': {},
                'average_score': 0
            }
    
    def migrate_from_json(self, json_path: Path) -> bool:
        """Migrate existing JSON data to database"""
        try:
            if not json_path.exists():
                return True
            
            with open(json_path, 'r') as f:
                data = json.load(f)
                cards = data.get('story_cards', [])
                
                if cards:
                    # Extract filename from path
                    filename = json_path.stem.replace('_cards', '') + '.pdf'
                    self.save_story_cards(filename, cards)
                    logger.info(f"Migrated {len(cards)} cards from {json_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error migrating from JSON: {e}")
            return False
    
    def get_all_pdfs(self) -> List[Dict[str, Any]]:
        """Get all PDFs with their card counts"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT p.id, p.filename, p.total_pages, p.upload_date,
                           COUNT(sc.id) as card_count
                    FROM pdfs p
                    LEFT JOIN story_cards sc ON p.id = sc.pdf_id
                    GROUP BY p.id
                    ORDER BY p.upload_date DESC
                """)
                
                pdfs = []
                for row in cursor.fetchall():
                    pdfs.append({
                        "id": row[0],
                        "filename": row[1],
                        "total_pages": row[2],
                        "upload_date": row[3],
                        "card_count": row[4]
                    })
                
                return pdfs
                
        except Exception as e:
            logger.error(f"Error getting PDFs: {e}")
            return []