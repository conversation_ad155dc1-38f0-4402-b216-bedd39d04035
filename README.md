# Storyteller Tactics Card Generator

A modern web application that transforms PDF documents into structured storytelling cards using Google's Gemini AI.

## Features

- **PDF Processing**: Upload and analyze PDF documents in configurable chunks
- **AI-Powered Analysis**: Uses Gemini 2.5 Flash Preview for advanced text analysis
- **Story Framework Categories**: 
  - Stories that Sell
  - Stories that Motivate
  - Stories that Convince
  - Stories that Connect
  - Stories that Explain
  - Stories that Lead
  - Stories that Impress
- **Advanced Features**:
  - Sentiment analysis
  - Entity recognition
  - Keyword extraction
  - Interest scoring
  - Resumable processing
- **Modern UI**: Built with Daisy UI (lofi theme) and Tailwind CSS
- **Smooth Animations**: Powered by anime.js
- **Export Options**: Markdown and JSON formats

## Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up Environment Variables**:
   - Copy `.env` file and add your Gemini API key:
   ```bash
   GEMINI_API_KEY=your_actual_api_key_here
   ```

3. **Run the Application**:
   ```bash
   python main.py
   ```
   
   The application will start on `http://localhost:8000`

## Usage

1. **Upload PDF**: Drag and drop or browse to select your PDF document
2. **Configure Processing**:
   - Set pages per chunk (default: 5)
   - Select page range
   - Choose story elements to focus on
   - Select storytelling frameworks
3. **Process Document**: Click "Start Processing" to begin AI analysis
4. **Explore Results**:
   - View overview statistics
   - Browse story cards with filters
   - Search through cards
   - Export results as Markdown or JSON

## API Endpoints

- `GET /` - Serve main application
- `GET /api/config` - Get configuration data
- `POST /api/upload` - Upload PDF file
- `POST /api/process/{filename}` - Start processing
- `GET /api/status/{process_id}` - Get processing status
- `GET /api/cards/{filename}` - Get story cards
- `GET /api/export/{filename}` - Export cards

## Story Card Structure

Each card contains:
- Title, framework, and category
- Emotional core and sentiment
- Detailed content and key insights
- Storyteller script (Hook, Narrative, Conclusion, Delivery)
- Key phrases and audience impact
- Interest score (1-10)
- Keywords and entities

## Technology Stack

- **Backend**: FastAPI with Python 3.x
- **Frontend**: Daisy UI (lofi theme) + Tailwind CSS
- **Animations**: anime.js
- **AI Model**: Google Gemini 2.5 Flash Preview
- **PDF Processing**: PyMuPDF

## File Structure

```
storyteller_cards/
├── pdfs/              # Uploaded PDF files
├── card_sets/         # Generated story cards (JSON)
├── story_patterns/    # Story pattern data
└── progress/          # Processing progress files

static/
├── index.html         # Main application UI
├── app.js            # Frontend JavaScript
└── styles.css        # Custom CSS styles
```

## Notes

- The application includes rate limiting protection with exponential backoff
- Processing can be paused and resumed
- All data is stored locally in the `storyteller_cards` directory
- Supports partial document processing with page range selection