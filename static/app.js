// Global variables
let currentFile = null;
let storyCards = [];
let processId = null;
let config = {};

// Initialize on page load
document.addEventListener('DOMContentLoaded', async () => {
    await checkHealth();
    await loadConfig();
    setupEventListeners();
    initializeAnimations();
    
    // Don't auto-load results on page load - wait for user action
    console.log('Page loaded, ready for user interaction');
});

// Check API health
async function checkHealth() {
    try {
        const response = await fetch('/api/health');
        const health = await response.json();
        
        if (health.db === 'unavailable') {
            showAlert('Warning: Database unavailable, using file storage as fallback', 'warning');
        }
        
        // Check directories
        const missingDirs = Object.entries(health.directories)
            .filter(([dir, exists]) => !exists)
            .map(([dir]) => dir);
        
        if (missingDirs.length > 0) {
            console.warn('Missing directories:', missingDirs);
        }
    } catch (error) {
        console.error('Health check failed:', error);
        showAlert('Warning: Could not connect to server', 'warning');
    }
}

// Load configuration from API
async function loadConfig() {
    try {
        const response = await fetch('/api/config');
        config = await response.json();
        populateFilters();
    } catch (error) {
        console.error('Error loading config:', error);
    }
}

// Setup event listeners
function setupEventListeners() {
    // File upload
    const fileInput = document.getElementById('fileInput');
    const fileDropArea = document.getElementById('fileDropArea');
    
    fileInput.addEventListener('change', handleFileSelect);
    
    // Drag and drop
    fileDropArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        fileDropArea.classList.add('drag-over');
    });
    
    fileDropArea.addEventListener('dragleave', () => {
        fileDropArea.classList.remove('drag-over');
    });
    
    fileDropArea.addEventListener('drop', (e) => {
        e.preventDefault();
        fileDropArea.classList.remove('drag-over');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFile(files[0]);
        }
    });
    
    // Tab navigation
    document.querySelectorAll('.tabs .tab').forEach(tab => {
        tab.addEventListener('click', () => {
            switchTab(tab.dataset.tab);
        });
    });
    
    // Search
    setupSearchInput();
    document.getElementById('searchInput').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
}

// Initialize animations
function initializeAnimations() {
    // Animate elements on scroll
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
            }
        });
    });
    
    document.querySelectorAll('.card').forEach(card => {
        observer.observe(card);
    });
}

// File handling
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        handleFile(file);
    }
}

async function handleFile(file) {
    if (!file.type.includes('pdf')) {
        showAlert('Please select a PDF file', 'error');
        return;
    }
    
    currentFile = file;
    
    // Upload file
    const formData = new FormData();
    formData.append('file', file);
    
    try {
        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error('Upload failed');
        }
        
        const data = await response.json();
        displayFileInfo(data);
        showConfigSection();
        
    } catch (error) {
        showAlert('Error uploading file: ' + error.message, 'error');
    }
}

// Display file information
function displayFileInfo(data) {
    const fileInfo = document.getElementById('fileInfo');
    const fileDetails = document.getElementById('fileDetails');
    
    fileDetails.innerHTML = `
        <strong>Filename:</strong> ${data.filename}<br>
        <strong>Size:</strong> ${(data.file_size / 1024).toFixed(2)} KB<br>
        <strong>Pages:</strong> ${data.total_pages}<br>
        ${data.existing_cards > 0 ? `<strong>Existing Cards:</strong> ${data.existing_cards}` : ''}
    `;
    
    fileInfo.classList.remove('hidden');
    
    // Update end page input
    document.getElementById('endPage').placeholder = data.total_pages;
}

// Show configuration section
function showConfigSection() {
    document.getElementById('configSection').classList.remove('hidden');
    populateStoryElements();
    populateFrameworks();
    
    // Animate section
    anime({
        targets: '#configSection',
        opacity: [0, 1],
        translateY: [20, 0],
        duration: 600,
        easing: 'easeOutQuad'
    });
}

// Populate story elements checkboxes
function populateStoryElements() {
    const container = document.getElementById('storyElements');
    container.innerHTML = '';
    
    Object.entries(config.story_elements || {}).forEach(([element, description]) => {
        const label = document.createElement('label');
        label.className = 'label cursor-pointer';
        label.innerHTML = `
            <span class="label-text" title="${description}">${element}</span>
            <input type="checkbox" class="checkbox checkbox-primary" value="${element}" checked>
        `;
        container.appendChild(label);
    });
}

// Populate frameworks checkboxes
function populateFrameworks() {
    const container = document.getElementById('frameworks');
    container.innerHTML = '';
    
    // Group frameworks by category
    const categories = {};
    Object.entries(config.framework_category_map || {}).forEach(([framework, category]) => {
        if (!categories[category]) {
            categories[category] = [];
        }
        categories[category].push(framework);
    });
    
    // Create sections for each category
    Object.entries(categories).forEach(([category, frameworks]) => {
        const section = document.createElement('div');
        section.className = 'mb-4';
        section.innerHTML = `
            <h4 class="font-semibold mb-2">${category}</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                ${frameworks.map(framework => `
                    <label class="label cursor-pointer">
                        <span class="label-text text-sm" title="${config.frameworks[framework] || ''}">${framework}</span>
                        <input type="checkbox" class="checkbox checkbox-sm checkbox-primary" value="${framework}" checked>
                    </label>
                `).join('')}
            </div>
        `;
        container.appendChild(section);
    });
}

// Start processing
async function startProcessing() {
    if (!currentFile) {
        showAlert('Please upload a file first', 'error');
        return;
    }
    
    // Get configuration values
    const chunkSize = parseInt(document.getElementById('chunkSize').value);
    const startPage = parseInt(document.getElementById('startPage').value);
    const endPage = parseInt(document.getElementById('endPage').value) || null;
    const resume = document.getElementById('resumeProcessing').checked;
    
    // Get selected story elements
    const storyElements = Array.from(document.querySelectorAll('#storyElements input:checked'))
        .map(cb => cb.value);
    
    // Get selected frameworks
    const frameworks = Array.from(document.querySelectorAll('#frameworks input:checked'))
        .map(cb => cb.value);
    
    // Prepare request
    const request = {
        chunk_size: chunkSize,
        start_page: startPage,
        end_page: endPage,
        story_elements: storyElements,
        frameworks: frameworks,
        resume: resume
    };
    
    try {
        const response = await fetch(`/api/process/${currentFile.name}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(request)
        });
        
        if (!response.ok) {
            throw new Error('Failed to start processing');
        }
        
        const data = await response.json();
        processId = data.process_id;
        
        // Show processing section
        showProcessingSection();
        
        // Start polling for status
        pollProcessingStatus();
        
    } catch (error) {
        showAlert('Error starting processing: ' + error.message, 'error');
    }
}

// Show processing section
function showProcessingSection() {
    document.getElementById('configSection').classList.add('hidden');
    document.getElementById('processingSection').classList.remove('hidden');
    
    anime({
        targets: '#processingSection',
        opacity: [0, 1],
        translateY: [20, 0],
        duration: 600,
        easing: 'easeOutQuad'
    });
}

// Poll processing status
async function pollProcessingStatus() {
    if (!processId) return;
    
    try {
        const response = await fetch(`/api/status/${processId}`);
        const status = await response.json();
        
        updateProcessingStatus(status);
        
        if (status.status === 'processing' || status.status === 'starting') {
            setTimeout(pollProcessingStatus, 1000);
        } else if (status.status === 'completed') {
            await loadResults();
        } else if (status.status === 'error') {
            showAlert('Processing error: ' + status.message, 'error');
            
            // Check if it's a rate limit error
            if (status.message.includes('429') || status.message.includes('RESOURCE_EXHAUSTED') || status.message.includes('quota')) {
                showAlert('API rate limit reached. Please try again later or reduce chunk size.', 'warning');
                
                // Show helpful suggestions
                const processingSection = document.getElementById('processingSection');
                processingSection.innerHTML += `
                    <div class="alert alert-warning mt-4">
                        <i class="fas fa-exclamation-triangle"></i>
                        <div>
                            <h3 class="font-bold">API Rate Limit Reached</h3>
                            <p>The Gemini API has rate limits. To continue:</p>
                            <ul class="list-disc ml-4">
                                <li>Wait a few minutes before retrying</li>
                                <li>Reduce the chunk size to 1-2 pages</li>
                                <li>Process fewer pages at a time</li>
                            </ul>
                        </div>
                    </div>
                `;
            }
        }
        
    } catch (error) {
        console.error('Error polling status:', error);
        setTimeout(pollProcessingStatus, 2000);
    }
}

// Update processing status display
function updateProcessingStatus(status) {
    document.getElementById('statusMessage').textContent = status.message;
    document.getElementById('progressPercent').textContent = `${Math.round(status.progress)}%`;
    document.getElementById('progressBar').value = status.progress;
    document.getElementById('currentChunk').textContent = status.current_chunk;
    document.getElementById('totalChunks').textContent = status.total_chunks;
    document.getElementById('cardsFound').textContent = status.cards_found;
    
    // Animate progress bar
    anime({
        targets: '#progressBar',
        value: status.progress,
        duration: 500,
        easing: 'easeInOutQuad'
    });
}

// Load results
async function loadResults() {
    try {
        if (!currentFile || !currentFile.name) {
            console.error('No file selected');
            showAlert('Please upload a file first', 'warning');
            return;
        }
        
        console.log('Loading results for:', currentFile.name);
        const response = await fetch(`/api/cards/${currentFile.name}`);
        
        if (!response.ok) {
            throw new Error(`Failed to load cards: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        storyCards = data.story_cards || [];
        
        console.log('Loaded story cards:', storyCards.length);
        
        if (storyCards.length === 0) {
            showAlert('No story cards found. Processing may have failed.', 'warning');
        } else {
            showAlert(`Successfully loaded ${storyCards.length} story cards! Click on the 'Story Cards' tab to view them.`, 'success');
        }
        
        // Show results section
        const processingSection = document.getElementById('processingSection');
        const resultsSection = document.getElementById('resultsSection');
        
        if (processingSection) {
            processingSection.classList.add('hidden');
        }
        
        if (resultsSection) {
            resultsSection.classList.remove('hidden');
            console.log('Results section made visible');
            
            // Also hide upload and config sections
            const uploadSection = document.getElementById('uploadSection');
            const configSection = document.getElementById('configSection');
            
            if (uploadSection) uploadSection.classList.add('hidden');
            if (configSection) configSection.classList.add('hidden');
        } else {
            console.error('Results section not found!');
        }
        
        // Update overview
        updateOverview();
        
        // Display cards
        displayCards(storyCards);
        
        // Populate filters
        populateFilters();
        
        // Switch to cards tab automatically if cards are found
        if (storyCards.length > 0) {
            console.log('Switching to cards tab');
            setTimeout(() => {
                switchTab('cards');
            }, 100);
        }
        
        // Load statistics
        await loadStatistics();
        
    } catch (error) {
        console.error('Error loading results:', error);
        showAlert('Error loading results: ' + error.message, 'error');
        
        // Show option to retry
        const processingSection = document.getElementById('processingSection');
        processingSection.innerHTML = `
            <div class="text-center">
                <p class="text-error mb-4">Failed to load results</p>
                <button class="btn btn-primary" onclick="location.reload()">Reload Page</button>
            </div>
        `;
    }
}

// Load statistics
async function loadStatistics() {
    try {
        const response = await fetch('/api/stats');
        const stats = await response.json();
        
        if (!stats.error) {
            // Update statistics display if available
            console.log('Statistics loaded:', stats);
        }
    } catch (error) {
        console.error('Error loading statistics:', error);
    }
}

// Update overview statistics
function updateOverview() {
    document.getElementById('totalCards').textContent = storyCards.length;
    
    const avgScore = storyCards.length > 0 
        ? (storyCards.reduce((sum, card) => sum + (card.interest_score || 0), 0) / storyCards.length).toFixed(1)
        : '0.0';
    document.getElementById('avgScore').textContent = avgScore;
    
    const categories = new Set(storyCards.map(card => card.category));
    document.getElementById('totalCategories').textContent = categories.size;
    
    // Category distribution
    const categoryCount = {};
    storyCards.forEach(card => {
        categoryCount[card.category] = (categoryCount[card.category] || 0) + 1;
    });
    
    const categoryChart = document.getElementById('categoryChart');
    categoryChart.innerHTML = '';
    
    Object.entries(categoryCount).forEach(([category, count]) => {
        const percentage = (count / storyCards.length * 100).toFixed(1);
        const bar = document.createElement('div');
        bar.className = 'mb-2';
        bar.innerHTML = `
            <div class="flex justify-between mb-1">
                <span class="text-sm font-medium">${category}</span>
                <span class="text-sm">${count} cards (${percentage}%)</span>
            </div>
            <div class="w-full bg-base-300 rounded-full h-2.5">
                <div class="bg-primary h-2.5 rounded-full" style="width: ${percentage}%"></div>
            </div>
        `;
        categoryChart.appendChild(bar);
        
        // Animate bar
        anime({
            targets: bar.querySelector('.bg-primary'),
            width: `${percentage}%`,
            duration: 1000,
            easing: 'easeOutQuad'
        });
    });
}

// Display story cards
function displayCards(cards) {
    console.log('displayCards called with', cards.length, 'cards');
    const container = document.getElementById('cardsList');
    if (!container) {
        console.error('cardsList container not found!');
        return;
    }
    
    // Clear existing content
    container.innerHTML = '';
    
    if (cards.length === 0) {
        container.innerHTML = '<div class="text-center p-8"><p class="text-lg opacity-70">No story cards to display</p></div>';
        return;
    }
    
    if (cards.length > 0) {
        console.log('First card:', cards[0]);
    }
    
    cards.forEach((card, index) => {
        try {
            const cardElement = createCardElement(card, index);
            container.appendChild(cardElement);
            
            // Animate card appearance
            anime({
                targets: cardElement,
                opacity: [0, 1],
                translateY: [20, 0],
                duration: 600,
                delay: Math.min(index * 50, 2000), // Cap delay at 2 seconds
                easing: 'easeOutQuad'
            });
        } catch (error) {
            console.error('Error creating card element:', error, card);
        }
    });
    
    console.log('Cards displayed successfully');
}

// Create card element
function createCardElement(card, index) {
    const scoreClass = card.interest_score >= 8 ? 'score-high' : 
                      card.interest_score >= 5 ? 'score-medium' : 'score-low';
    
    const categoryClass = getCategoryClass(card.category);
    
    const div = document.createElement('div');
    div.className = 'card bg-base-100 shadow-xl story-card';
    div.style.opacity = '0';  // Start with opacity 0 for animation
    div.innerHTML = `
        <div class="card-body">
            <div class="flex justify-between items-start">
                <h3 class="card-title text-lg">${card.title}</h3>
                <div class="interest-score ${scoreClass}">${card.interest_score}</div>
            </div>
            
            <div class="flex flex-wrap gap-2 mb-2">
                <span class="badge ${categoryClass}">${card.category}</span>
                <span class="badge badge-outline">${card.framework}</span>
                ${card.sentiment ? `<span class="badge badge-ghost">${card.sentiment}</span>` : ''}
            </div>
            
            <p class="text-sm opacity-80 mb-2">${card.emotional_core}</p>
            
            <p class="text-sm mb-4">${card.content ? card.content.substring(0, 200) + '...' : card.key_insight || 'No content available'}</p>
            
            <div class="card-actions justify-end">
                <button class="btn btn-sm btn-primary" onclick="showCardDetails(${index})">
                    View Details
                </button>
            </div>
        </div>
    `;
    
    return div;
}

// Get category class
function getCategoryClass(category) {
    const categoryMap = {
        'Stories that Sell': 'category-sell',
        'Stories that Motivate': 'category-motivate',
        'Stories that Convince': 'category-convince',
        'Stories that Connect': 'category-connect',
        'Stories that Explain': 'category-explain',
        'Stories that Lead': 'category-lead',
        'Stories that Impress': 'category-impress'
    };
    return categoryMap[category] || 'badge-primary';
}

// Show card details in modal
function showCardDetails(index) {
    const card = storyCards[index];
    const modal = document.getElementById('storyCardModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalContent = document.getElementById('modalContent');
    
    modalTitle.textContent = card.title;
    
    modalContent.innerHTML = `
        <div class="space-y-4">
            <div>
                <h4 class="font-semibold mb-2">Overview</h4>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <span class="text-sm opacity-70">Category:</span>
                        <p class="font-medium">${card.category}</p>
                    </div>
                    <div>
                        <span class="text-sm opacity-70">Framework:</span>
                        <p class="font-medium">${card.framework}</p>
                    </div>
                    <div>
                        <span class="text-sm opacity-70">Emotional Core:</span>
                        <p class="font-medium">${card.emotional_core}</p>
                    </div>
                    <div>
                        <span class="text-sm opacity-70">Interest Score:</span>
                        <p class="font-medium">${card.interest_score}/10</p>
                    </div>
                </div>
            </div>
            
            ${card.content ? `
            <div>
                <h4 class="font-semibold mb-2">Content</h4>
                <p class="text-sm">${card.content}</p>
            </div>` : ''}
            
            <div>
                <h4 class="font-semibold mb-2">Key Insight</h4>
                <p class="text-sm">${card.key_insight}</p>
            </div>
            
            <div>
                <h4 class="font-semibold mb-2">Contextual Relevance</h4>
                <p class="text-sm">${card.contextual_relevance}</p>
            </div>
            
            <div>
                <h4 class="font-semibold mb-2">Key Phrases</h4>
                <ul class="list-disc list-inside text-sm">
                    ${card.key_phrases.map(phrase => `<li>"${phrase}"</li>`).join('')}
                </ul>
            </div>
            
            <div>
                <h4 class="font-semibold mb-2">Storyteller Script</h4>
                <div class="space-y-3">
                    ${card.script_hook ? `
                        <div class="script-section">
                            <h5>Hook</h5>
                            <p class="text-sm">${card.script_hook}</p>
                        </div>
                    ` : ''}
                    ${card.script_narrative ? `
                        <div class="script-section">
                            <h5>Narrative</h5>
                            <p class="text-sm">${card.script_narrative}</p>
                        </div>
                    ` : ''}
                    ${card.script_conclusion ? `
                        <div class="script-section">
                            <h5>Conclusion</h5>
                            <p class="text-sm">${card.script_conclusion}</p>
                        </div>
                    ` : ''}
                    ${card.script_delivery ? `
                        <div class="script-section">
                            <h5>Delivery</h5>
                            <p class="text-sm">${card.script_delivery}</p>
                        </div>
                    ` : ''}
                </div>
            </div>
            
            <div>
                <h4 class="font-semibold mb-2">Additional Information</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="opacity-70">Audience:</span>
                        <p>${card.audience.join(', ')}</p>
                    </div>
                    <div>
                        <span class="opacity-70">Pages:</span>
                        <p>${card.page_reference.join(', ')}</p>
                    </div>
                    ${card.keywords.length > 0 ? `
                        <div class="col-span-2">
                            <span class="opacity-70">Keywords:</span>
                            <p>${card.keywords.join(', ')}</p>
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;
    
    modal.showModal();
}

// Populate filters
function populateFilters() {
    if (storyCards.length === 0) return;
    
    // Categories
    const categories = [...new Set(storyCards.map(card => card.category))];
    const categoryFilter = document.getElementById('categoryFilter');
    categoryFilter.innerHTML = categories.map(cat => 
        `<option value="${cat}">${cat}</option>`
    ).join('');
    
    // Frameworks
    const frameworks = [...new Set(storyCards.map(card => card.framework))];
    const frameworkFilter = document.getElementById('frameworkFilter');
    frameworkFilter.innerHTML = frameworks.map(fw => 
        `<option value="${fw}">${fw}</option>`
    ).join('');
    
    // Sentiments
    const sentiments = [...new Set(storyCards.map(card => card.sentiment).filter(s => s))];
    const sentimentFilter = document.getElementById('sentimentFilter');
    sentimentFilter.innerHTML = sentiments.map(sentiment => 
        `<option value="${sentiment}">${sentiment}</option>`
    ).join('');
}

// Apply filters
function applyFilters() {
    const selectedCategories = Array.from(document.getElementById('categoryFilter').selectedOptions)
        .map(opt => opt.value);
    const selectedFrameworks = Array.from(document.getElementById('frameworkFilter').selectedOptions)
        .map(opt => opt.value);
    const selectedSentiments = Array.from(document.getElementById('sentimentFilter').selectedOptions)
        .map(opt => opt.value);
    const minScore = parseInt(document.getElementById('scoreFilter').value);
    
    let filteredCards = storyCards;
    
    if (selectedCategories.length > 0) {
        filteredCards = filteredCards.filter(card => selectedCategories.includes(card.category));
    }
    if (selectedFrameworks.length > 0) {
        filteredCards = filteredCards.filter(card => selectedFrameworks.includes(card.framework));
    }
    if (selectedSentiments.length > 0) {
        filteredCards = filteredCards.filter(card => selectedSentiments.includes(card.sentiment));
    }
    filteredCards = filteredCards.filter(card => card.interest_score >= minScore);
    
    displayCards(filteredCards);
}

// Reset filters
function resetFilters() {
    document.getElementById('categoryFilter').selectedIndex = -1;
    document.getElementById('frameworkFilter').selectedIndex = -1;
    document.getElementById('sentimentFilter').selectedIndex = -1;
    document.getElementById('scoreFilter').value = 1;
    displayCards(storyCards);
}

// Perform search
async function performSearch() {
    const query = document.getElementById('searchInput').value.trim();
    if (!query) {
        document.getElementById('searchResults').innerHTML = '';
        return;
    }
    
    try {
        // Show loading state
        const resultsContainer = document.getElementById('searchResults');
        resultsContainer.innerHTML = '<div class="text-center"><span class="loading loading-spinner loading-lg"></span></div>';
        
        // Prepare search request
        const searchRequest = {
            query: query,
            pdf_name: currentFile ? currentFile.name : null
        };
        
        // Add filters if in results view
        if (storyCards.length > 0) {
            const selectedCategories = Array.from(document.getElementById('categoryFilter').selectedOptions)
                .map(opt => opt.value);
            const selectedFrameworks = Array.from(document.getElementById('frameworkFilter').selectedOptions)
                .map(opt => opt.value);
            const selectedSentiments = Array.from(document.getElementById('sentimentFilter').selectedOptions)
                .map(opt => opt.value);
            const minScore = parseInt(document.getElementById('scoreFilter').value);
            
            if (selectedCategories.length > 0) searchRequest.categories = selectedCategories;
            if (selectedFrameworks.length > 0) searchRequest.frameworks = selectedFrameworks;
            if (selectedSentiments.length > 0) searchRequest.sentiments = selectedSentiments;
            if (minScore > 1) searchRequest.min_score = minScore;
        }
        
        // Call search API
        const response = await fetch('/api/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(searchRequest)
        });
        
        if (!response.ok) {
            throw new Error('Search failed');
        }
        
        const searchData = await response.json();
        const results = searchData.results;
        
        if (results.length === 0) {
            resultsContainer.innerHTML = '<p class="text-center opacity-70">No matching cards found</p>';
        } else {
            resultsContainer.innerHTML = `<p class="mb-4">Found ${results.length} matching cards:</p>`;
            results.forEach((card, index) => {
                const cardElement = createCardElement(card, index);
                resultsContainer.appendChild(cardElement);
            });
        }
        
    } catch (error) {
        console.error('Search error:', error);
        document.getElementById('searchResults').innerHTML = '<p class="text-center text-error">Search failed. Please try again.</p>';
        showAlert('Search error: ' + error.message, 'error');
    }
}

// Setup search with debounce
let searchTimeout;
function setupSearchInput() {
    const searchInput = document.getElementById('searchInput');
    
    searchInput.addEventListener('input', async (e) => {
        clearTimeout(searchTimeout);
        const query = e.target.value.trim();
        
        if (query.length >= 2) {
            searchTimeout = setTimeout(async () => {
                await showSearchSuggestions(query);
            }, 300);
        } else {
            hideSuggestions();
        }
    });
}

// Show search suggestions
async function showSearchSuggestions(query) {
    try {
        const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(query)}`);
        const data = await response.json();
        
        if (data.suggestions && data.suggestions.length > 0) {
            // Create or update suggestions dropdown
            let suggestionsDiv = document.getElementById('searchSuggestions');
            if (!suggestionsDiv) {
                suggestionsDiv = document.createElement('div');
                suggestionsDiv.id = 'searchSuggestions';
                suggestionsDiv.className = 'absolute z-10 w-full bg-base-100 shadow-lg rounded-md mt-1 max-h-48 overflow-y-auto';
                document.getElementById('searchInput').parentElement.appendChild(suggestionsDiv);
            }
            
            suggestionsDiv.innerHTML = data.suggestions.map(suggestion => `
                <div class="px-4 py-2 hover:bg-base-200 cursor-pointer" onclick="selectSuggestion('${suggestion.replace(/'/g, "\\'")}')">  
                    ${suggestion}
                </div>
            `).join('');
            
            suggestionsDiv.style.display = 'block';
        }
    } catch (error) {
        console.error('Error getting suggestions:', error);
    }
}

// Hide suggestions
function hideSuggestions() {
    const suggestionsDiv = document.getElementById('searchSuggestions');
    if (suggestionsDiv) {
        suggestionsDiv.style.display = 'none';
    }
}

// Select suggestion
function selectSuggestion(suggestion) {
    document.getElementById('searchInput').value = suggestion;
    hideSuggestions();
    performSearch();
}

// Export cards
async function exportCards(format) {
    try {
        const response = await fetch(`/api/export/${currentFile.name}?format=${format}`);
        
        if (!response.ok) {
            throw new Error('Export failed');
        }
        
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = currentFile.name.replace('.pdf', `_storyteller_cards.${format === 'markdown' ? 'md' : 'json'}`);
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        showAlert(`Successfully exported ${format} file`, 'success');
        
    } catch (error) {
        showAlert('Error exporting: ' + error.message, 'error');
    }
}

// Switch tabs
function switchTab(tabName) {
    console.log('Switching to tab:', tabName);
    
    // Update tab buttons
    document.querySelectorAll('.tabs .tab').forEach(tab => {
        if (tab.dataset.tab === tabName) {
            tab.classList.add('tab-active');
        } else {
            tab.classList.remove('tab-active');
        }
    });
    
    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    const targetTab = document.getElementById(`${tabName}Tab`);
    if (targetTab) {
        targetTab.classList.remove('hidden');
        console.log(`${tabName}Tab made visible`);
        
        // Check visibility of cardsTab specifically
        const cardsTab = document.getElementById('cardsTab');
        const cardsList = document.getElementById('cardsList');
        console.log('cardsTab element:', cardsTab);
        console.log('cardsTab hidden?', cardsTab?.classList.contains('hidden'));
        console.log('cardsList element:', cardsList);
        console.log('cardsList children:', cardsList?.children.length);
        
        // If switching to cards tab, ensure cards are displayed
        if (tabName === 'cards' && storyCards.length > 0) {
            console.log('Re-displaying cards in cards tab');
            displayCards(storyCards);
        }
    } else {
        console.error(`Tab not found: ${tabName}Tab`);
    }
}

// Show help modal
function showHelp() {
    document.getElementById('helpModal').showModal();
}

// Manual load results for debugging
async function manualLoadResults() {
    console.log('Manual load triggered - fetching available PDFs');
    
    try {
        // First, get the list of PDFs from the database
        const pdfResponse = await fetch('/api/pdfs');
        if (!pdfResponse.ok) {
            throw new Error('Failed to fetch PDF list');
        }
        
        const pdfData = await pdfResponse.json();
        console.log('Available PDFs:', pdfData.pdfs);
        
        if (!pdfData.pdfs || pdfData.pdfs.length === 0) {
            showAlert('No PDFs found in database', 'warning');
            return;
        }
        
        // Use the first PDF (most recent)
        const mostRecentPdf = pdfData.pdfs[0];
        currentFile = {
            name: mostRecentPdf.filename
        };
        
        console.log('Loading results for:', currentFile.name);
        showAlert(`Loading ${mostRecentPdf.card_count} cards from ${mostRecentPdf.filename}`, 'info');
        
        // Show results section first
        document.getElementById('uploadSection').classList.add('hidden');
        document.getElementById('configSection').classList.add('hidden');
        document.getElementById('processingSection').classList.add('hidden');
        document.getElementById('resultsSection').classList.remove('hidden');
        
        await loadResults();
    } catch (error) {
        console.error('Manual load error:', error);
        showAlert('Error loading results: ' + error.message, 'error');
    }
}

// Show alert
function showAlert(message, type = 'info') {
    const alertClass = type === 'error' ? 'alert-error' : 
                      type === 'success' ? 'alert-success' : 
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    
    const alert = document.createElement('div');
    alert.className = `alert ${alertClass} fixed top-4 right-4 w-96 z-50 shadow-lg`;
    alert.innerHTML = `
        <i class="fas fa-${type === 'error' ? 'exclamation-circle' : 
                         type === 'success' ? 'check-circle' : 
                         type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    document.body.appendChild(alert);
    
    anime({
        targets: alert,
        opacity: [0, 1],
        translateX: [100, 0],
        duration: 400,
        easing: 'easeOutQuad'
    });
    
    const duration = type === 'error' ? 5000 : 3000;
    
    setTimeout(() => {
        anime({
            targets: alert,
            opacity: 0,
            translateX: 100,
            duration: 400,
            easing: 'easeInQuad',
            complete: () => alert.remove()
        });
    }, duration);
}