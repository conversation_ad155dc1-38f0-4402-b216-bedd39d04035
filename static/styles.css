/* Custom animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Animation classes */
.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-slide-in {
    animation: slideIn 0.4s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* File drop area styling */
.file-drop-area {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.file-drop-area:hover {
    border-color: oklch(var(--p)) !important;
    background-color: oklch(var(--b2));
}

.file-drop-area.drag-over {
    border-color: oklch(var(--p)) !important;
    background-color: oklch(var(--p) / 0.1);
}

/* Card hover effects */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Story card styling */
.story-card {
    border-left: 4px solid oklch(var(--p));
    transition: all 0.3s ease;
}

.story-card:hover {
    border-left-width: 8px;
}

/* Tab content transitions */
.tab-content {
    animation: fadeIn 0.3s ease-out;
}

/* Progress bar animation */
.progress {
    position: relative;
    overflow: hidden;
}

.progress::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: progress-shine 2s linear infinite;
}

@keyframes progress-shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Stat card animations */
.stat {
    transition: all 0.3s ease;
}

.stat:hover {
    transform: scale(1.02);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: oklch(var(--b2));
}

::-webkit-scrollbar-thumb {
    background: oklch(var(--p) / 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: oklch(var(--p) / 0.7);
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid oklch(var(--p) / 0.2);
    border-radius: 50%;
    border-top-color: oklch(var(--p));
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Category badge colors */
.category-sell {
    background-color: #e74c3c;
    color: white;
}

.category-motivate {
    background-color: #3498db;
    color: white;
}

.category-convince {
    background-color: #2ecc71;
    color: white;
}

.category-connect {
    background-color: #9b59b6;
    color: white;
}

.category-explain {
    background-color: #f39c12;
    color: white;
}

.category-lead {
    background-color: #1abc9c;
    color: white;
}

.category-impress {
    background-color: #34495e;
    color: white;
}

/* Interest score badge */
.interest-score {
    font-size: 1.2rem;
    font-weight: bold;
    min-width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.score-high {
    background-color: #2ecc71;
    color: white;
}

.score-medium {
    background-color: #f39c12;
    color: white;
}

.score-low {
    background-color: #e74c3c;
    color: white;
}

/* Script section styling */
.script-section {
    border-left: 3px solid oklch(var(--p) / 0.3);
    padding-left: 1rem;
    margin: 1rem 0;
}

.script-section h5 {
    color: oklch(var(--p));
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Search highlight */
.search-highlight {
    background-color: oklch(var(--wa));
    padding: 0 2px;
    border-radius: 2px;
}

/* Visibility helpers */
.hidden {
    display: none !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modal-box {
        max-width: calc(100vw - 2rem);
    }
    
    .stats {
        grid-template-columns: 1fr;
    }
}