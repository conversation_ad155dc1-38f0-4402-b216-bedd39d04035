<!DOCTYPE html>
<html lang="en" data-theme="lofi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Storyteller Tactics Card Generator</title>
    
    <!-- Tailwind CSS & Daisy UI -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.6.0/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Anime.js -->
    <script src="https://cdn.jsdelivr.net/npm/animejs@3.2.1/lib/anime.min.js"></script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="/static/styles.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <style>
        /* Search suggestions dropdown styling */
        #searchSuggestions {
            border: 1px solid var(--fallback-bc,oklch(var(--bc)/0.2));
        }
        
        /* Make search input container relative for absolute positioning */
        .search-container {
            position: relative;
        }
    </style>
</head>
<body class="min-h-screen bg-base-200">
    <!-- Navigation -->
    <nav class="navbar bg-base-100 shadow-lg">
        <div class="navbar-start">
            <a class="btn btn-ghost text-xl">
                <i class="fas fa-book-open text-primary"></i>
                <span class="ml-2">Storyteller Tactics</span>
            </a>
        </div>
        <div class="navbar-end">
            <button class="btn btn-ghost btn-circle" onclick="showHelp()">
                <i class="fas fa-question-circle text-lg"></i>
            </button>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container mx-auto px-4 py-8">
        <!-- Hero Section -->
        <div class="hero bg-base-100 rounded-lg shadow-xl mb-8 animate-fade-in">
            <div class="hero-content text-center py-12">
                <div class="max-w-md">
                    <h1 class="text-5xl font-bold mb-4">Transform Your PDFs into Story Cards</h1>
                    <p class="text-lg opacity-80 mb-6">Extract powerful storytelling elements from your documents using AI</p>
                    
                    <!-- Button to load existing results -->
                    <button class="btn btn-primary btn-sm" onclick="manualLoadResults()">
                        <i class="fas fa-folder-open mr-2"></i>
                        Load Previous Results
                    </button>
                </div>
            </div>
        </div>

        <!-- Upload Section -->
        <div id="uploadSection" class="card bg-base-100 shadow-xl mb-8">
            <div class="card-body">
                <h2 class="card-title text-2xl mb-4">
                    <i class="fas fa-upload text-primary"></i>
                    Upload Your Document
                </h2>
                
                <div class="form-control">
                    <div class="file-drop-area border-2 border-dashed border-base-300 rounded-lg p-12 text-center hover:border-primary transition-colors" 
                         id="fileDropArea">
                        <i class="fas fa-file-pdf text-6xl text-base-300 mb-4"></i>
                        <p class="text-lg mb-2">Drag and drop your PDF here</p>
                        <p class="text-sm opacity-70 mb-4">or</p>
                        <input type="file" id="fileInput" accept=".pdf" class="hidden">
                        <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                            Browse Files
                        </button>
                    </div>
                </div>
                
                <div id="fileInfo" class="mt-4 hidden">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <div>
                            <h3 class="font-bold">File Details</h3>
                            <p id="fileDetails"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Configuration Section -->
        <div id="configSection" class="card bg-base-100 shadow-xl mb-8 hidden">
            <div class="card-body">
                <h2 class="card-title text-2xl mb-4">
                    <i class="fas fa-cog text-primary"></i>
                    Processing Configuration
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Pages per Chunk</span>
                            <span class="label-text-alt">How many pages to analyze together</span>
                        </label>
                        <input type="number" id="chunkSize" value="3" min="1" max="10" class="input input-bordered">
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Page Range</span>
                        </label>
                        <div class="flex gap-2">
                            <input type="number" id="startPage" value="1" min="1" placeholder="Start" class="input input-bordered flex-1">
                            <input type="number" id="endPage" placeholder="End" class="input input-bordered flex-1">
                        </div>
                    </div>
                </div>
                
                <div class="divider">Advanced Options</div>
                
                <!-- Story Elements -->
                <div class="collapse collapse-arrow bg-base-200">
                    <input type="checkbox" />
                    <div class="collapse-title text-xl font-medium">
                        Story Elements
                    </div>
                    <div class="collapse-content">
                        <div id="storyElements" class="grid grid-cols-2 gap-2">
                            <!-- Story elements checkboxes will be inserted here -->
                        </div>
                    </div>
                </div>
                
                <!-- Frameworks -->
                <div class="collapse collapse-arrow bg-base-200 mt-2">
                    <input type="checkbox" />
                    <div class="collapse-title text-xl font-medium">
                        Story Frameworks
                    </div>
                    <div class="collapse-content">
                        <div id="frameworks" class="space-y-4">
                            <!-- Framework checkboxes will be inserted here -->
                        </div>
                    </div>
                </div>
                
                <div class="form-control mt-4">
                    <label class="label cursor-pointer">
                        <span class="label-text">Resume from last position</span>
                        <input type="checkbox" id="resumeProcessing" class="checkbox checkbox-primary">
                    </label>
                </div>
                
                <div class="card-actions justify-end mt-6">
                    <button id="processBtn" class="btn btn-primary btn-lg" onclick="startProcessing()">
                        <i class="fas fa-play mr-2"></i>
                        Start Processing
                    </button>
                </div>
            </div>
        </div>

        <!-- Processing Status -->
        <div id="processingSection" class="card bg-base-100 shadow-xl mb-8 hidden">
            <div class="card-body">
                <h2 class="card-title text-2xl mb-4">
                    <i class="fas fa-spinner fa-spin text-primary"></i>
                    Processing Your Document
                </h2>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span id="statusMessage" class="text-lg">Starting processing...</span>
                        <span id="progressPercent" class="text-2xl font-bold">0%</span>
                    </div>
                    
                    <progress id="progressBar" class="progress progress-primary w-full" value="0" max="100"></progress>
                    
                    <div class="stats stats-vertical lg:stats-horizontal shadow">
                        <div class="stat">
                            <div class="stat-figure text-primary">
                                <i class="fas fa-file-alt text-3xl"></i>
                            </div>
                            <div class="stat-title">Current Chunk</div>
                            <div class="stat-value text-primary" id="currentChunk">0</div>
                            <div class="stat-desc">of <span id="totalChunks">0</span> chunks</div>
                        </div>
                        
                        <div class="stat">
                            <div class="stat-figure text-secondary">
                                <i class="fas fa-cards text-3xl"></i>
                            </div>
                            <div class="stat-title">Cards Found</div>
                            <div class="stat-value text-secondary" id="cardsFound">0</div>
                            <div class="stat-desc">story cards extracted</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="hidden">
            <!-- Debug button -->
            <div class="mb-4">
                <button class="btn btn-sm btn-ghost" onclick="manualLoadResults()">
                    <i class="fas fa-sync mr-2"></i>
                    Reload Results
                </button>
            </div>
            <!-- Results Tabs -->
            <div class="tabs tabs-boxed mb-6">
                <a class="tab tab-active" data-tab="overview">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Overview
                </a>
                <a class="tab" data-tab="cards">
                    <i class="fas fa-cards mr-2"></i>
                    Story Cards
                </a>
                <a class="tab" data-tab="search">
                    <i class="fas fa-search mr-2"></i>
                    Search
                </a>
                <a class="tab" data-tab="export">
                    <i class="fas fa-download mr-2"></i>
                    Export
                </a>
            </div>

            <!-- Tab Content -->
            <div id="tabContent">
                <!-- Overview Tab -->
                <div id="overviewTab" class="tab-content">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div class="stat bg-base-100 shadow-xl rounded-lg">
                            <div class="stat-figure text-primary">
                                <i class="fas fa-book text-4xl"></i>
                            </div>
                            <div class="stat-title">Total Cards</div>
                            <div class="stat-value text-primary" id="totalCards">0</div>
                        </div>
                        
                        <div class="stat bg-base-100 shadow-xl rounded-lg">
                            <div class="stat-figure text-secondary">
                                <i class="fas fa-star text-4xl"></i>
                            </div>
                            <div class="stat-title">Average Score</div>
                            <div class="stat-value text-secondary" id="avgScore">0.0</div>
                        </div>
                        
                        <div class="stat bg-base-100 shadow-xl rounded-lg">
                            <div class="stat-figure text-accent">
                                <i class="fas fa-layer-group text-4xl"></i>
                            </div>
                            <div class="stat-title">Categories</div>
                            <div class="stat-value text-accent" id="totalCategories">0</div>
                        </div>
                    </div>
                    
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            <h3 class="card-title">Category Distribution</h3>
                            <div id="categoryChart" class="space-y-2">
                                <!-- Category distribution will be inserted here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cards Tab -->
                <div id="cardsTab" class="tab-content hidden">
                    <div class="mb-6">
                        <!-- Filters -->
                        <div class="card bg-base-100 shadow-xl mb-4">
                            <div class="card-body">
                                <h3 class="card-title mb-4">Filters</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text">Category</span>
                                        </label>
                                        <select id="categoryFilter" class="select select-bordered" multiple>
                                            <!-- Options will be populated dynamically -->
                                        </select>
                                    </div>
                                    
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text">Framework</span>
                                        </label>
                                        <select id="frameworkFilter" class="select select-bordered" multiple>
                                            <!-- Options will be populated dynamically -->
                                        </select>
                                    </div>
                                    
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text">Minimum Score</span>
                                        </label>
                                        <input type="range" id="scoreFilter" min="1" max="10" value="1" class="range range-primary">
                                        <div class="w-full flex justify-between text-xs px-2">
                                            <span>1</span>
                                            <span>5</span>
                                            <span>10</span>
                                        </div>
                                    </div>
                                    
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text">Sentiment</span>
                                        </label>
                                        <select id="sentimentFilter" class="select select-bordered" multiple>
                                            <!-- Options will be populated dynamically -->
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="mt-4 flex justify-end gap-2">
                                    <button class="btn btn-ghost" onclick="resetFilters()">Reset</button>
                                    <button class="btn btn-primary" onclick="applyFilters()">Apply Filters</button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Cards List -->
                        <div id="cardsList" class="space-y-4">
                            <!-- Story cards will be inserted here -->
                        </div>
                    </div>
                </div>

                <!-- Search Tab -->
                <div id="searchTab" class="tab-content hidden">
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            <h3 class="card-title mb-4">Search Story Cards</h3>
                            
                            <div class="form-control search-container">
                                <input type="text" id="searchInput" placeholder="Enter search terms..." 
                                       class="input input-bordered input-lg w-full">
                            </div>
                            
                            <div class="form-control mt-4">
                                <label class="label">
                                    <span class="label-text">Search in:</span>
                                </label>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                                    <label class="label cursor-pointer">
                                        <span class="label-text">Title</span>
                                        <input type="checkbox" class="checkbox checkbox-primary" checked value="title">
                                    </label>
                                    <label class="label cursor-pointer">
                                        <span class="label-text">Content</span>
                                        <input type="checkbox" class="checkbox checkbox-primary" checked value="content">
                                    </label>
                                    <label class="label cursor-pointer">
                                        <span class="label-text">Key Insight</span>
                                        <input type="checkbox" class="checkbox checkbox-primary" checked value="key_insight">
                                    </label>
                                    <label class="label cursor-pointer">
                                        <span class="label-text">Script</span>
                                        <input type="checkbox" class="checkbox checkbox-primary" checked value="storyteller_script">
                                    </label>
                                </div>
                            </div>
                            
                            <button class="btn btn-primary mt-4" onclick="performSearch()">
                                <i class="fas fa-search mr-2"></i>
                                Search
                            </button>
                            
                            <div id="searchResults" class="mt-6 space-y-4">
                                <!-- Search results will be displayed here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Export Tab -->
                <div id="exportTab" class="tab-content hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="card bg-base-100 shadow-xl">
                            <div class="card-body">
                                <h3 class="card-title mb-4">
                                    <i class="fas fa-file-markdown text-primary"></i>
                                    Markdown Export
                                </h3>
                                <p class="opacity-80 mb-4">Export your story cards as a formatted Markdown document</p>
                                <button class="btn btn-primary" onclick="exportCards('markdown')">
                                    <i class="fas fa-download mr-2"></i>
                                    Download Markdown
                                </button>
                            </div>
                        </div>
                        
                        <div class="card bg-base-100 shadow-xl">
                            <div class="card-body">
                                <h3 class="card-title mb-4">
                                    <i class="fas fa-file-code text-secondary"></i>
                                    JSON Export
                                </h3>
                                <p class="opacity-80 mb-4">Export raw data for integration with other tools</p>
                                <button class="btn btn-secondary" onclick="exportCards('json')">
                                    <i class="fas fa-download mr-2"></i>
                                    Download JSON
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Help Modal -->
    <dialog id="helpModal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg">How to Use</h3>
            <div class="py-4 space-y-4">
                <div>
                    <h4 class="font-semibold">1. Upload Your PDF</h4>
                    <p class="text-sm opacity-80">Drag and drop or browse to select your PDF document</p>
                </div>
                <div>
                    <h4 class="font-semibold">2. Configure Processing</h4>
                    <p class="text-sm opacity-80">Adjust chunk size, page range, and select story elements</p>
                </div>
                <div>
                    <h4 class="font-semibold">3. Process Document</h4>
                    <p class="text-sm opacity-80">AI will analyze your document and extract story cards</p>
                </div>
                <div>
                    <h4 class="font-semibold">4. Explore & Export</h4>
                    <p class="text-sm opacity-80">Filter, search, and export your story cards</p>
                </div>
            </div>
            <div class="modal-action">
                <form method="dialog">
                    <button class="btn">Close</button>
                </form>
            </div>
        </div>
    </dialog>

    <!-- Story Card Modal -->
    <dialog id="storyCardModal" class="modal">
        <div class="modal-box w-11/12 max-w-5xl">
            <h3 class="font-bold text-lg" id="modalTitle">Story Card</h3>
            <div class="py-4" id="modalContent">
                <!-- Card content will be inserted here -->
            </div>
            <div class="modal-action">
                <form method="dialog">
                    <button class="btn">Close</button>
                </form>
            </div>
        </div>
    </dialog>

    <!-- JavaScript -->
    <script src="/static/app.js"></script>
</body>
</html>