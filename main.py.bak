from fastapi import Fast<PERSON><PERSON>, UploadFile, File, HTTPException, BackgroundTasks, Query
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import os
import json
import fitz  # PyMuPDF
from datetime import datetime
from pathlib import Path
import tempfile
import time
import random
from google import genai
from google.genai import types
from dotenv import load_dotenv
import asyncio
from concurrent.futures import ThreadPoolExecutor
import logging
import traceback
from database import StoryDatabase

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(title="Storyteller Tactics Card Generator")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration Constants
BASE_DIR = Path("storyteller_cards")
PDF_DIR = BASE_DIR / "pdfs"
CARD_SETS_DIR = BASE_DIR / "card_sets"
STORY_PATTERNS_DIR = BASE_DIR / "story_patterns"
PROGRESS_DIR = BASE_DIR / "progress"
DB_DIR = BASE_DIR / "database"
MODEL = "gemini-2.5-flash-preview-05-20"

# Create directories with error handling
try:
    for directory in [PDF_DIR, CARD_SETS_DIR, STORY_PATTERNS_DIR, PROGRESS_DIR, DB_DIR]:
        directory.mkdir(parents=True, exist_ok=True)
        logger.info(f"Directory ensured: {directory}")
except Exception as e:
    logger.error(f"Error creating directories: {e}")
    # Continue anyway, individual operations will handle missing directories

# Initialize database storage
try:
    db = StoryDatabase(DB_DIR / "storyteller.db")
    logger.info("Database initialized successfully")
    # Migrate existing JSON files on startup
    json_files = list(CARD_SETS_DIR.glob("*_cards.json"))
    for json_file in json_files:
        if db.migrate_from_json(json_file):
            logger.info(f"Migrated {json_file.name} to database")
except Exception as e:
    logger.error(f"Error initializing database: {e}")
    db = None

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Rate limiting constants
MAX_RETRIES = 5
INITIAL_RETRY_DELAY = 2
MAX_RETRY_DELAY = 60

# Storyteller Tactics Categories
STORY_CATEGORIES = {
    "Stories that Sell": "Build trust and convince people of your ability to deliver.",
    "Stories that Motivate": "Inspire people to support your ideas and take action.",
    "Stories that Convince": "Explain complex information to a non-expert audience.",
    "Stories that Connect": "Foster empathy and understanding by showing different perspectives.",
    "Stories that Explain": "Make abstract strategies understandable and relevant.",
    "Stories that Lead": "Build a stronger and more cohesive team.",
    "Stories that Impress": "Present ideas confidently and clearly."
}

# Storyteller Tactics Frameworks
STORY_FRAMEWORKS = {
    "Simple Sales Stories": "Share relatable success stories of helping others.",
    "Social Proof": "Use trends, prototypes, and testimonials to strengthen your case.",
    "Rags to Riches": "Tell optimistic stories with the customer as the central figure.",
    "Pitch Perfect": "Condense your message into a concise elevator pitch.",
    "Audience Profile": "Understand your audience and their problems.",
    
    "Dragon & the City": "Explain your overarching goals and vision.",
    "Drive Stories": "Articulate your motivations and connect with audience.",
    "Three Great Conflicts": "Identify and address obstacles through human struggles.",
    "Innovation Curve": "Reassure your audience about risks involved.",
    "No Easy Way": "Provide a realistic outlook on challenges ahead.",
    
    "Three is the Magic Number": "Prioritize key facts using patterns of three.",
    "That's Funny": "Share excitement and insights behind discoveries.",
    "Data Detectives": "Present data in a narrative format.",
    "Trust Me, I'm an Expert": "Demonstrate credibility through stories.",
    "Hero & Guide": "Position yourself as the expert guide.",
    
    "Story Listening": "Understand others by listening to experiences.",
    "Abstractions": "Observe behavior to understand deeper knowledge.",
    "Universal Stories": "Find common ground across cultures.",
    "Story-ish Conversations": "Look for stories in everyday interactions.",
    "Circle of Life": "Develop relatable stories based on universal characters.",
    
    "Order & Chaos": "Show where your strategy fits in changing world.",
    "Good & Evil": "Define important battles your strategy addresses.",
    "What's it About?": "Explain relevance of strategy to colleagues.",
    "Rolls Royce Moment": "Illustrate strategy with vivid detail.",
    "Story Hooks": "Make strategy engaging with compelling openings.",
    
    "Curious Tales": "Discover what motivates team members.",
    "Man in a Hole": "Frame teamwork as epic journey.",
    "Emotional Dashboard": "Find stories in highs and lows of projects.",
    "Thoughtful Failures": "Extract lessons from mistakes.",
    "Story Bank": "Collect and share valuable team stories.",
    
    "Movie Time": "Tell a story, not just present facts.",
    "Five Ts": "Structure story effectively.",
    "Show and Tell": "Make visuals and narration work together.",
    "Cut to the Chase": "Have backup plan if presentation falters.",
    "Secrets & Puzzles": "Engage audience with undiscovered information."
}

# Framework to Category mapping
FRAMEWORK_CATEGORY_MAP = {
    "Simple Sales Stories": "Stories that Sell",
    "Social Proof": "Stories that Sell",
    "Rags to Riches": "Stories that Sell",
    "Pitch Perfect": "Stories that Sell",
    "Audience Profile": "Stories that Sell",
    
    "Dragon & the City": "Stories that Motivate",
    "Drive Stories": "Stories that Motivate",
    "Three Great Conflicts": "Stories that Motivate",
    "Innovation Curve": "Stories that Motivate",
    "No Easy Way": "Stories that Motivate",
    
    "Three is the Magic Number": "Stories that Convince",
    "That's Funny": "Stories that Convince",
    "Data Detectives": "Stories that Convince",
    "Trust Me, I'm an Expert": "Stories that Convince",
    "Hero & Guide": "Stories that Convince",
    
    "Story Listening": "Stories that Connect",
    "Abstractions": "Stories that Connect",
    "Universal Stories": "Stories that Connect",
    "Story-ish Conversations": "Stories that Connect",
    "Circle of Life": "Stories that Connect",
    
    "Order & Chaos": "Stories that Explain",
    "Good & Evil": "Stories that Explain",
    "What's it About?": "Stories that Explain",
    "Rolls Royce Moment": "Stories that Explain",
    "Story Hooks": "Stories that Explain",
    
    "Curious Tales": "Stories that Lead",
    "Man in a Hole": "Stories that Lead",
    "Emotional Dashboard": "Stories that Lead",
    "Thoughtful Failures": "Stories that Lead",
    "Story Bank": "Stories that Lead",
    
    "Movie Time": "Stories that Impress",
    "Five Ts": "Stories that Impress",
    "Show and Tell": "Stories that Impress",
    "Cut to the Chase": "Stories that Impress",
    "Secrets & Puzzles": "Stories that Impress"
}

# Story elements
STORY_ELEMENTS = {
    "Characters": "People or entities that drive the narrative",
    "Setting": "The context, environment, or backdrop",
    "Conflict": "The central problem or challenge",
    "Resolution": "How problems are solved",
    "Emotion": "The feelings evoked or expressed",
    "Surprise": "Unexpected turns or revelations",
    "Metaphor": "Symbolic comparisons",
    "Data Point": "Compelling statistics or facts",
    "Quotable Moment": "Memorable phrases worth repeating",
    "Transformation": "Changes in perspective or situation"
}

# Pydantic models
class StoryCard(BaseModel):
    title: str
    framework: str
    category: str
    emotional_core: str
    content: str
    key_insight: str
    contextual_relevance: str
    storyteller_script: str
    script_hook: str = ""
    script_narrative: str = ""
    script_conclusion: str = ""
    script_delivery: str = ""
    key_phrases: List[str]
    audience_impact: str
    interest_score: int
    audience: List[str]
    source: str
    page_reference: List[int]
    entities: List[str] = []
    sentiment: str = ""
    keywords: List[str] = []

class ProcessingStatus(BaseModel):
    status: str
    progress: float
    current_chunk: int
    total_chunks: int
    cards_found: int
    message: str

class ProcessingRequest(BaseModel):
    chunk_size: int = 5
    start_page: int = 1
    end_page: Optional[int] = None
    story_elements: List[str] = []
    frameworks: List[str] = []
    resume: bool = False

class SearchRequest(BaseModel):
    query: str
    pdf_name: Optional[str] = None
    categories: List[str] = []
    frameworks: List[str] = []
    sentiments: List[str] = []
    min_score: Optional[int] = None
    limit: Optional[int] = 100

# Global processing status
processing_status = {}
executor = ThreadPoolExecutor(max_workers=4)

def initialize_gemini_client():
    """Initialize Gemini client with API key"""
    api_key = os.environ.get("GEMINI_API_KEY")
    if not api_key:
        raise ValueError("GEMINI_API_KEY not found in environment variables")
    return genai.Client(api_key=api_key)

async def process_with_retry(client, prompt, config, max_retries=MAX_RETRIES):
    """Process API request with exponential backoff retry logic"""
    for attempt in range(max_retries):
        try:
            response = client.models.generate_content(
                model=MODEL,
                contents=prompt,
                config=config,
            )
            return response
        except Exception as e:
            error_str = str(e)
            if "429" in error_str or "RESOURCE_EXHAUSTED" in error_str:
                if attempt < max_retries - 1:
                    delay = min(INITIAL_RETRY_DELAY * (2 ** attempt) + random.uniform(0, 1), MAX_RETRY_DELAY)
                    logger.warning(f"Rate limit hit. Retrying in {delay:.1f} seconds (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"Maximum retries reached. Error: {e}")
                    raise
            else:
                logger.error(f"API Error: {e}")
                raise
    raise Exception("Maximum retries exceeded")

async def analyze_story_potential(client, chunk_text: str, pages: List[int], frameworks: List[str], story_elements: List[str]) -> Dict:
    """Analyze text chunk to determine story potential"""
    
    framework_descriptions = [f"{fw}: {STORY_FRAMEWORKS[fw]}" for fw in frameworks if fw in STORY_FRAMEWORKS]
    element_descriptions = [f"{element}: {STORY_ELEMENTS[element]}" for element in story_elements if element in STORY_ELEMENTS]
    
    system_prompt = f"""Analyze this chunk of text from a book to determine if it contains potential material for storytelling.

Perform an in-depth analysis including:
1. Sentiment analysis - identify the overall emotional tone
2. Named entity recognition - identify key people, organizations, locations, events
3. Keyword extraction - identify the most important themes and topics
4. Story potential assessment - evaluate if the text contains compelling narrative elements

Focus on identifying if this text contains one or more of these story elements:
{'; '.join(element_descriptions)}

Specifically, evaluate if this text could be used to create story cards fitting any of these storytelling frameworks:
{'; '.join(framework_descriptions)}

Your response should be in JSON format with the following structure:
{{
  "has_story_potential": true/false,
  "story_frameworks": ["Framework 1", "Framework 2", ...],
  "sentiment": "Overall emotional tone",
  "entities": ["Entity 1", "Entity 2", ...],  
  "keywords": ["Theme 1", "Theme 2", ...],
  "reasoning": "Detailed explanation"
}}"""
    
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_text(text=f"{system_prompt}\n\nText chunk to analyze: {chunk_text[:10000]}"),
            ],
        ),
    ]
    
    generate_content_config = types.GenerateContentConfig(
        temperature=0.2,
        thinking_config=types.ThinkingConfig(thinking_budget=0),
        response_mime_type="application/json",
    )
    
    try:
        response = await process_with_retry(client, contents, generate_content_config)
        return json.loads(response.text)
    except Exception as e:
        logger.error(f"Error analyzing story potential: {e}")
        return {"has_story_potential": False, "story_frameworks": [], "sentiment": "", "entities": [], "keywords": []}

async def extract_story_cards(client, chunk_text: str, pages: List[int], frameworks: List[str], analysis_result: Dict) -> List[Dict]:
    """Extract story cards from text chunk"""
    
    if not analysis_result.get("has_story_potential") or not analysis_result.get("story_frameworks"):
        return []
    
    framework_descriptions = [f"{fw}: {STORY_FRAMEWORKS[fw]}" for fw in analysis_result["story_frameworks"] if fw in STORY_FRAMEWORKS]
    
    system_prompt = f"""Create compelling story cards based on the text chunk provided.

For each potential story in this text, create a card that follows the Storyteller Tactics framework. Focus on these frameworks:
{'; '.join(framework_descriptions)}

For each story card, provide all required fields including:
- Title, framework, category, emotional core
- Detailed content with vivid details
- Key insight and contextual relevance
- A storyteller script with Hook, Narrative, Powerful Conclusion, and Delivery sections
- Key phrases, audience impact, interest score (1-10)
- Entities, keywords, and sentiment

Based on preliminary analysis, this text:
- Has these named entities: {', '.join(analysis_result.get('entities', [])[:5])}
- Contains these themes: {', '.join(analysis_result.get('keywords', [])[:5])}
- Has an overall {analysis_result.get('sentiment', 'mixed')} sentiment tone

Your response should be in JSON format with a "story_cards" array containing the card objects."""
    
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_text(text=f"{system_prompt}\n\nText chunk (from pages {pages[0]}-{pages[-1]}):\n{chunk_text[:15000]}"),
            ],
        ),
    ]
    
    generate_content_config = types.GenerateContentConfig(
        temperature=0.7,
        thinking_config=types.ThinkingConfig(thinking_budget=0),
        response_mime_type="application/json",
    )
    
    try:
        response = await process_with_retry(client, contents, generate_content_config)
        response_json = json.loads(response.text)
        story_cards = response_json.get("story_cards", [])
        
        # Process each card
        for card in story_cards:
            # Ensure category is correctly mapped
            framework = card.get("framework", "")
            if framework in FRAMEWORK_CATEGORY_MAP:
                card["category"] = FRAMEWORK_CATEGORY_MAP[framework]
            
            # Parse storyteller script sections
            if "storyteller_script" in card and not card.get("script_hook"):
                full_script = card["storyteller_script"]
                if "Hook:" in full_script and "Narrative:" in full_script:
                    try:
                        hook_start = full_script.find("Hook:") + 5
                        hook_end = full_script.find("Narrative:")
                        card["script_hook"] = full_script[hook_start:hook_end].strip()
                        
                        narrative_start = full_script.find("Narrative:") + 10
                        narrative_end = full_script.find("Powerful Conclusion:")
                        if narrative_end == -1:
                            narrative_end = full_script.find("Conclusion:")
                        card["script_narrative"] = full_script[narrative_start:narrative_end].strip()
                        
                        conclusion_start = max(full_script.find("Powerful Conclusion:") + 20, 
                                              full_script.find("Conclusion:") + 11)
                        conclusion_end = full_script.find("Delivery:")
                        card["script_conclusion"] = full_script[conclusion_start:conclusion_end].strip()
                        
                        delivery_start = full_script.find("Delivery:") + 9
                        card["script_delivery"] = full_script[delivery_start:].strip()
                    except:
                        pass
            
            # Set page reference
            card["page_reference"] = pages
        
        return story_cards
        
    except Exception as e:
        logger.error(f"Error extracting story cards: {e}")
        return []

async def process_pdf_async(pdf_path: str, pdf_name: str, request: ProcessingRequest, process_id: str):
    """Process PDF asynchronously with enhanced error handling"""
    global processing_status
    
    try:
        # Initialize client
        client = initialize_gemini_client()
        
        # Load existing cards if available
        story_cards = []
        if request.resume and storage:
            story_cards = storage.get_cards(pdf_name)
            logger.info(f"Resuming with {len(story_cards)} existing cards")
        
        # Open PDF
        pdf_document = fitz.open(pdf_path)
        total_pages = pdf_document.page_count
        
        # Adjust page range
        start_page = max(0, request.start_page - 1)
        end_page = min(request.end_page or total_pages, total_pages)
        
        # Create chunks
        chunks = []
        for i in range(start_page, end_page, request.chunk_size):
            chunk_pages = []
            chunk_text = ""
            
            for j in range(i, min(i + request.chunk_size, end_page)):
                page = pdf_document[j]
                page_text = page.get_text()
                chunk_text += f"\n\n--- PAGE {j + 1} ---\n\n{page_text}"
                chunk_pages.append(j + 1)
            
            chunks.append({"pages": chunk_pages, "text": chunk_text})
        
        total_chunks = len(chunks)
        
        # Load progress if resuming
        start_chunk = 0
        if request.resume and storage:
            progress_data = storage.get_processing_status(pdf_name)
            if progress_data:
                start_chunk = progress_data.get("last_processed_chunk", 0)
                logger.info(f"Resuming from chunk {start_chunk}")
        
        # Process chunks
        for chunk_idx in range(start_chunk, total_chunks):
            # Update status
            processing_status[process_id] = ProcessingStatus(
                status="processing",
                progress=(chunk_idx / total_chunks) * 100,
                current_chunk=chunk_idx + 1,
                total_chunks=total_chunks,
                cards_found=len(story_cards),
                message=f"Processing chunk {chunk_idx + 1}/{total_chunks}"
            )
            
            # Analyze chunk
            chunk = chunks[chunk_idx]
            analysis_result = await analyze_story_potential(
                client, chunk["text"], chunk["pages"],
                request.frameworks or list(STORY_FRAMEWORKS.keys()),
                request.story_elements or list(STORY_ELEMENTS.keys())
            )
            
            # Extract cards if potential found
            if analysis_result.get("has_story_potential"):
                new_cards = await extract_story_cards(
                    client, chunk["text"], chunk["pages"],
                    request.frameworks or list(STORY_FRAMEWORKS.keys()),
                    analysis_result
                )
                story_cards.extend(new_cards)
            
            # Save progress periodically
            if chunk_idx % 3 == 0 or chunk_idx == total_chunks - 1:
                if storage:
                    storage.save_cards(pdf_name, story_cards)
                    storage.save_processing_status(pdf_name, chunk_idx, total_chunks)
                else:
                    # Fallback to JSON
                    try:
                        cards_file = CARD_SETS_DIR / f"{pdf_name.replace('.pdf', '')}_cards.json"
                        with open(cards_file, 'w') as f:
                            json.dump({"story_cards": story_cards}, f, indent=2)
                    except Exception as e:
                        logger.error(f"Error saving cards to JSON: {e}")
        
        # Final save
        if storage:
            storage.save_cards(pdf_name, story_cards)
            storage.save_processing_status(pdf_name, total_chunks, total_chunks, "completed")
        else:
            # Fallback to JSON
            cards_file = CARD_SETS_DIR / f"{pdf_name.replace('.pdf', '')}_cards.json"
            with open(cards_file, 'w') as f:
                json.dump({"story_cards": story_cards}, f, indent=2)
        
        # Update final status
        processing_status[process_id] = ProcessingStatus(
            status="completed",
            progress=100,
            current_chunk=total_chunks,
            total_chunks=total_chunks,
            cards_found=len(story_cards),
            message=f"Processing complete! Found {len(story_cards)} story cards."
        )
        
        pdf_document.close()
        
    except Exception as e:
        error_msg = f"Error processing PDF: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        
        if storage:
            storage.save_processing_status(pdf_name, 0, 0, "error", str(e))
        
        processing_status[process_id] = ProcessingStatus(
            status="error",
            progress=0,
            current_chunk=0,
            total_chunks=0,
            cards_found=0,
            message=f"Error: {str(e)}"
        )

# API Routes
@app.get("/")
async def root():
    """Serve the main HTML page"""
    return FileResponse("static/index.html")

@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "storage": "available" if storage else "unavailable",
        "directories": {
            "pdf_dir": PDF_DIR.exists(),
            "card_sets_dir": CARD_SETS_DIR.exists(),
            "progress_dir": PROGRESS_DIR.exists(),
            "db_dir": DB_DIR.exists()
        }
    }

@app.get("/api/config")
async def get_config():
    """Get configuration data"""
    return {
        "categories": STORY_CATEGORIES,
        "frameworks": STORY_FRAMEWORKS,
        "framework_category_map": FRAMEWORK_CATEGORY_MAP,
        "story_elements": STORY_ELEMENTS
    }

@app.post("/api/upload")
async def upload_pdf(file: UploadFile = File(...)):
    """Upload a PDF file with enhanced error handling"""
    if not file.filename.endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are allowed")
    
    try:
        # Ensure directory exists
        PDF_DIR.mkdir(parents=True, exist_ok=True)
        
        # Save file
        file_path = PDF_DIR / file.filename
        content = await file.read()
        
        with open(file_path, "wb") as f:
            f.write(content)
        
        logger.info(f"Uploaded file: {file.filename} ({len(content)} bytes)")
        
        # Get PDF info
        try:
            pdf_document = fitz.open(file_path)
            total_pages = pdf_document.page_count
            pdf_document.close()
        except Exception as e:
            logger.error(f"Error reading PDF {file.filename}: {e}")
            raise HTTPException(status_code=400, detail=f"Error reading PDF: {str(e)}")
        
        # Check for existing cards
        existing_cards = 0
        if storage:
            existing_cards = len(storage.get_cards(file.filename))
        else:
            # Fallback to JSON check
            cards_file = CARD_SETS_DIR / f"{file.filename.replace('.pdf', '')}_cards.json"
            if cards_file.exists():
                try:
                    with open(cards_file, 'r') as f:
                        data = json.load(f)
                        existing_cards = len(data.get("story_cards", []))
                except Exception as e:
                    logger.error(f"Error reading existing cards: {e}")
        
        return {
            "filename": file.filename,
            "total_pages": total_pages,
            "file_size": len(content),
            "existing_cards": existing_cards
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        raise HTTPException(status_code=500, detail=f"Upload error: {str(e)}")

@app.post("/api/process/{filename}")
async def process_pdf(filename: str, request: ProcessingRequest, background_tasks: BackgroundTasks):
    """Start processing a PDF"""
    file_path = PDF_DIR / filename
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found")
    
    # Generate process ID
    process_id = f"{filename}_{int(time.time())}"
    
    # Initialize status
    processing_status[process_id] = ProcessingStatus(
        status="starting",
        progress=0,
        current_chunk=0,
        total_chunks=0,
        cards_found=0,
        message="Starting processing..."
    )
    
    # Start background processing
    background_tasks.add_task(
        process_pdf_async,
        str(file_path),
        filename,
        request,
        process_id
    )
    
    return {"process_id": process_id}

@app.get("/api/status/{process_id}")
async def get_status(process_id: str):
    """Get processing status"""
    if process_id not in processing_status:
        raise HTTPException(status_code=404, detail="Process not found")
    
    return processing_status[process_id]

@app.get("/api/cards/{filename}")
async def get_cards(filename: str):
    """Get story cards for a PDF with enhanced error handling"""
    try:
        if storage:
            story_cards = storage.get_cards(filename)
            return {"story_cards": story_cards}
        else:
            # Fallback to JSON
            cards_file = CARD_SETS_DIR / f"{filename.replace('.pdf', '')}_cards.json"
            if not cards_file.exists():
                logger.warning(f"No cards found for {filename}")
                return {"story_cards": []}
            
            with open(cards_file, 'r') as f:
                return json.load(f)
    except Exception as e:
        logger.error(f"Error getting cards for {filename}: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving cards: {str(e)}")

@app.get("/api/export/{filename}")
async def export_cards(filename: str, format: str = "markdown"):
    """Export story cards"""
    try:
        # Get cards from storage
        if storage:
            story_cards = storage.get_cards(filename)
        else:
            # Fallback to JSON
            cards_file = CARD_SETS_DIR / f"{filename.replace('.pdf', '')}_cards.json"
            if not cards_file.exists():
                raise HTTPException(status_code=404, detail="No cards found for this file")
            
            with open(cards_file, 'r') as f:
                data = json.load(f)
                story_cards = data.get("story_cards", [])
        
        if not story_cards:
            raise HTTPException(status_code=404, detail="No cards found for this file")
    except Exception as e:
        logger.error(f"Error exporting cards: {e}")
        raise HTTPException(status_code=500, detail=f"Export error: {str(e)}")
    
    if format == "markdown":
        export_content = generate_markdown_export(story_cards, filename)
        return FileResponse(
            path=export_content,
            filename=f"{filename.replace('.pdf', '')}_storyteller_cards.md",
            media_type="text/markdown"
        )
    elif format == "json":
        # Create temporary JSON file for export
        export_json_path = CARD_SETS_DIR / f"{filename.replace('.pdf', '')}_export.json"
        with open(export_json_path, 'w') as f:
            json.dump({"story_cards": story_cards}, f, indent=2)
        
        return FileResponse(
            path=export_json_path,
            filename=f"{filename.replace('.pdf', '')}_cards.json",
            media_type="application/json"
        )
    else:
        raise HTTPException(status_code=400, detail="Invalid export format")

def generate_markdown_export(story_cards: List[Dict], filename: str) -> Path:
    """Generate markdown export of story cards"""
    export_path = CARD_SETS_DIR / f"{filename.replace('.pdf', '')}_export.md"
    
    # Group by category
    categories = {}
    for card in story_cards:
        category = card.get("category", "Uncategorized")
        if category not in categories:
            categories[category] = []
        categories[category].append(card)
    
    with open(export_path, 'w', encoding='utf-8') as f:
        f.write(f"# Storyteller Tactics Cards: {filename}\n")
        f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Table of contents
        f.write("## Table of Contents\n\n")
        for category in categories:
            f.write(f"- [{category}](#{category.lower().replace(' ', '-')})\n")
        f.write("\n---\n\n")
        
        for category, cards in categories.items():
            f.write(f"## {category}\n\n")
            if category in STORY_CATEGORIES:
                f.write(f"_{STORY_CATEGORIES[category]}_\n\n")
            
            for i, card in enumerate(cards):
                f.write(f"### {card.get('title', f'Card {i+1}')}\n\n")
                f.write(f"**Framework:** {card.get('framework', 'N/A')}  \n")
                f.write(f"**Emotional Core:** {card.get('emotional_core', 'N/A')}  \n")
                
                if card.get('keywords'):
                    f.write(f"**Keywords:** {', '.join(card.get('keywords', []))}  \n")
                if card.get('sentiment'):
                    f.write(f"**Sentiment:** {card.get('sentiment', '')}  \n")
                
                f.write("\n**Content:**  \n")
                f.write(f"{card.get('content', '')}  \n\n")
                f.write(f"**Key Insight:**  \n{card.get('key_insight', '')}  \n\n")
                f.write(f"**Contextual Relevance:**  \n{card.get('contextual_relevance', '')}  \n\n")
                
                f.write("**Key Phrases:**  \n")
                for phrase in card.get('key_phrases', []):
                    f.write(f"- \"{phrase}\"  \n")
                f.write("\n")
                
                f.write(f"**Audience Impact:**  \n{card.get('audience_impact', '')}  \n\n")
                
                # Storyteller script
                f.write("**Storyteller Script:**  \n\n```\n")
                script_hook = card.get('script_hook', '')
                script_narrative = card.get('script_narrative', '')
                script_conclusion = card.get('script_conclusion', '')
                script_delivery = card.get('script_delivery', '')
                
                if script_hook or script_narrative:
                    f.write(f"Hook: \n{script_hook}\n\n")
                    f.write(f"Narrative: \n{script_narrative}\n\n")
                    f.write(f"Powerful Conclusion: \n{script_conclusion}\n\n")
                    f.write(f"Delivery: \n{script_delivery}\n")
                else:
                    f.write(f"{card.get('storyteller_script', '')}\n")
                f.write("```\n\n")
                
                f.write(f"**Interest Score:** {card.get('interest_score', 'N/A')}/10  \n")
                f.write(f"**Suitable for:** {', '.join(card.get('audience', ['General audience']))}  \n")
                f.write(f"**Pages:** {', '.join([str(p) for p in card.get('page_reference', [])])}  \n\n")
                f.write("---\n\n")
    
    return export_path

@app.post("/api/search")
async def search_cards(request: SearchRequest):
    """Search story cards with advanced filters"""
    try:
        if not storage:
            raise HTTPException(status_code=503, detail="Search service unavailable")
        
        # Prepare filters
        filters = {}
        if request.categories:
            filters['categories'] = request.categories
        if request.frameworks:
            filters['frameworks'] = request.frameworks
        if request.sentiments:
            filters['sentiments'] = request.sentiments
        if request.min_score is not None:
            filters['min_score'] = request.min_score
        if request.limit:
            filters['limit'] = request.limit
        
        # Perform search
        results = storage.search_cards(
            query=request.query,
            pdf_name=request.pdf_name,
            filters=filters
        )
        
        return {
            "results": results,
            "total": len(results),
            "query": request.query,
            "filters": filters
        }
        
    except Exception as e:
        logger.error(f"Error searching cards: {e}")
        raise HTTPException(status_code=500, detail=f"Search error: {str(e)}")

@app.get("/api/search/suggestions")
async def get_search_suggestions(q: str = Query(..., min_length=2)):
    """Get search suggestions based on partial query"""
    try:
        if not storage:
            return {"suggestions": []}
        
        # Get all unique keywords and phrases from recent cards
        all_cards = storage.search_cards("", limit=100)
        
        suggestions = set()
        q_lower = q.lower()
        
        for card in all_cards:
            # Check keywords
            for keyword in card.get('keywords', []):
                if keyword.lower().startswith(q_lower):
                    suggestions.add(keyword)
            
            # Check key phrases
            for phrase in card.get('key_phrases', []):
                if phrase.lower().startswith(q_lower):
                    suggestions.add(phrase)
            
            # Check entities
            for entity in card.get('entities', []):
                if entity.lower().startswith(q_lower):
                    suggestions.add(entity)
        
        # Sort and limit suggestions
        sorted_suggestions = sorted(list(suggestions))[:10]
        
        return {"suggestions": sorted_suggestions}
        
    except Exception as e:
        logger.error(f"Error getting suggestions: {e}")
        return {"suggestions": []}

@app.get("/api/stats")
async def get_statistics():
    """Get overall statistics about stored cards"""
    try:
        if not storage:
            return {"error": "Statistics unavailable"}
        
        # Get all cards
        all_cards = storage.search_cards("", limit=10000)
        
        # Calculate statistics
        total_cards = len(all_cards)
        if total_cards == 0:
            return {
                "total_cards": 0,
                "total_pdfs": 0,
                "categories": {},
                "frameworks": {},
                "avg_interest_score": 0,
                "top_keywords": []
            }
        
        # Count by category and framework
        categories = {}
        frameworks = {}
        keywords_count = {}
        total_score = 0
        pdfs = set()
        
        for card in all_cards:
            # Categories
            cat = card.get('category', 'Unknown')
            categories[cat] = categories.get(cat, 0) + 1
            
            # Frameworks
            fw = card.get('framework', 'Unknown')
            frameworks[fw] = frameworks.get(fw, 0) + 1
            
            # Keywords
            for kw in card.get('keywords', []):
                keywords_count[kw] = keywords_count.get(kw, 0) + 1
            
            # Score
            total_score += card.get('interest_score', 0)
            
            # PDFs
            pdfs.add(card.get('source', card.get('pdf_name', '')))
        
        # Top keywords
        top_keywords = sorted(keywords_count.items(), key=lambda x: x[1], reverse=True)[:20]
        
        return {
            "total_cards": total_cards,
            "total_pdfs": len(pdfs),
            "categories": categories,
            "frameworks": frameworks,
            "avg_interest_score": round(total_score / total_cards, 2),
            "top_keywords": [k[0] for k in top_keywords]
        }
        
    except Exception as e:
        logger.error(f"Error getting statistics: {e}")
        return {"error": f"Statistics error: {str(e)}"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)